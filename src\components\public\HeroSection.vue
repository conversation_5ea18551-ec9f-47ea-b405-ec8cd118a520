<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import AITriggerButton from '../ai/AITriggerButton.vue';
import { triggerSignIn } from '../../services/unifiedAuthService';

const logoUrl = ref('/logo.png');
const isVisible = ref(false);
const router = useRouter();
const authStore = useAuthStore();

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated);

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const navigateToCommunity = async () => {
  try {
    await router.push('/virtual-community?tab=feed');
  } catch (error) {
    console.error('Navigation error to community:', error);
  }
};

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section')
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const goToDashboard = async () => {
  try {
    await router.push('/dashboard');
  } catch (error) {
    console.error('Navigation error to dashboard:', error);
  }
};

const triggerSignInDialog = () => {
  triggerSignIn();
};

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 100);
});
</script>

<template>
  <section class="hero-section relative-position" role="banner" aria-label="Welcome to ZbInnovation">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="hero-content q-pa-md">
            <!-- Centered content with logo positioned above translucent section -->
            <div class="centered-content text-center" :class="{ 'visible': isVisible }">
              <!-- Logo positioned 50% above the translucent section -->
              <div class="logo-container q-mb-md animate-slide-down">
                <div class="logo-bg">
                  <img :src="logoUrl" alt="ZbInnovation Logo" class="mini-logo" style="height: 80px;" width="80" height="80">
                </div>
              </div>

              <!-- Translucent background section occupying full column -->
              <div class="translucent-section q-pa-lg animate-slide-up">
                <h1 class="text-h2 innovation-text zb-secondary text-weight-bold q-mb-xs animate-slide-right">
                  Innovation Community
                </h1>
                <p class="text-body1 q-mb-xs text-weight-light">
                  Be part of the innovation revolution; connect with like-minded players in the innovation ecosystem.
                </p>
                <p class="text-h6 q-mb-xs text-weight-bold">
                  <strong>Innovators, Investors, Mentors, Industry Experts, Academic Student, Academic Institutions, Government</strong>
                </p>
                <p class="text-body2 q-mb-sm text-weight-light">
                  Join us, and be part of an amazing community
                </p>

                <!-- Action buttons inside the translucent section -->
                <div class="action-buttons-inside q-mt-sm">
                  <div class="row q-gutter-sm justify-center">
                    <!-- AI Learn More trigger button -->
                    <div class="col-12 col-md-auto">
                      <AITriggerButton
                        trigger-key="learn_more_platform"
                        label="Learn More"
                        icon="psychology"
                        color="secondary"
                        size="md"
                        outline
                        rounded
                        tooltip="Get AI assistance to learn more about the platform"
                        context="hero-section"
                        class="cta-button"
                      />
                    </div>

                    <!-- Auth-aware buttons -->
                    <template v-if="!isAuthenticated">
                      <!-- Sign In button - only show for non-authenticated users -->
                      <div class="col-12 col-md-auto">
                        <q-btn
                          @click="triggerSignInDialog"
                          color="primary"
                          label="Sign In"
                          size="md"
                          rounded
                          class="text-weight-bold signin-btn animate-bounce cta-button"
                          aria-label="Sign in to ZbInnovation"
                        />
                      </div>
                    </template>

                    <template v-else>
                      <!-- Dashboard button - only show for authenticated users -->
                      <div class="col-12 col-md-auto">
                        <q-btn
                          @click="goToDashboard"
                          color="primary"
                          label="Go to Dashboard"
                          size="md"
                          rounded
                          class="text-weight-bold dashboard-btn animate-bounce cta-button"
                          aria-label="Go to your dashboard"
                        />
                      </div>
                    </template>

                    <!-- Explore Community button - always show -->
                    <div class="col-12 col-md-auto">
                      <q-btn
                        @click="navigateToCommunity"
                        label="Explore Community"
                        class="zb-btn-secondary zb-btn-bounce"
                        aria-label="Explore the community">
                        <q-tooltip>Discover our community features</q-tooltip>
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
    <div class="wavy-line animate-wave" aria-hidden="true">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" preserveAspectRatio="none">
        <path fill="none" stroke="#a4ca39" stroke-width="3" stroke-dasharray="5,5" d="M0,160L60,170.7C120,181,240,203,360,197.3C480,192,600,160,720,138.7C840,117,960,107,1080,122.7C1200,139,1320,181,1380,202.7L1440,224" />
      </svg>
    </div>
    <div class="white-diagonal-cut" aria-hidden="true" />
  </section>
</template>

<style scoped>
.hero-section {
  min-height: 600px;
  background-image: url('https://ext.same-assets.com/4258758033/1505177164.png');
  background-size: cover;
  background-position: center;
  color: white;
  position: relative;
  overflow: hidden;
  padding: 0;
  margin-top: -64px; /* Compensate for header height */
}

.container {
  width: 100%;
  max-width: 1400px;
  z-index: 2;
  position: relative;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 600px;
  padding-top: 64px; /* Add padding equal to header height */
}

.centered-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 80px; /* Add margin to position logo at the edge */
}

.logo-container {
  position: relative;
  z-index: 3;
  margin-bottom: -35px; /* Position 50% of logo above translucent section */
}

.logo-bg {
  background-color: white;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  margin: 0 auto;
}

.translucent-section {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  width: 100%;
  padding: 40px 40px 30px; /* Reduced top padding to push text up */
  color: white;
  text-align: center;
}

.mini-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.innovation-text {
  font-weight: 700;
  font-size: 3rem;
  line-height: 1;
  color: #a4ca39;
  text-align: center;
}

.action-buttons {
  width: 100%;
  display: flex;
  justify-content: center;
}

.action-buttons-inside {
  width: 100%;
  display: flex;
  justify-content: center;
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px; /* Fixed height to ensure same size */
  font-weight: 600;
}

.explore-btn, .signin-btn {
  border-radius: 5px;
  min-width: 160px;
}

.wavy-line {
  position: absolute;
  top: 40%;
  left: 0;
  width: 100%;
  height: 200px;
  z-index: 1;
  opacity: 0.5;
}

.white-diagonal-cut {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 200px;
  background-color: white;
  clip-path: polygon(100% 0, 0% 100%, 100% 100%);
  z-index: 3;
}

@media (max-width: 1023px) {
  .innovation-text {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  .hero-section {
    padding-top: 40px;
    padding-bottom: 0;
  }

  .innovation-text {
    font-size: 2.2rem;
  }

  .translucent-section {
    margin-top: 20px;
    padding: 35px 20px 25px;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .logo-container {
    margin-bottom: -25px;
  }

  .centered-content {
    margin-top: 60px;
  }

  .action-buttons {
    margin-top: 20px;
  }
}

@media (max-width: 599px) {
  .hero-section {
    min-height: auto;
    padding-top: 40px;
    margin-top: -40px; /* Adjusted for mobile */
    padding-bottom: 40px;
    background-position: 70% center;
  }

  .hero-content {
    padding-top: 30px !important;
    padding-bottom: 20px !important;
  }

  .translucent-section {
    margin: 20px 0 !important;
    padding: 35px 15px 20px !important;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.1);
  }

  .cta-button {
    width: calc(100% - 16px) !important;
    margin: 4px 8px !important;
  }

  .action-buttons-inside .row {
    flex-direction: column !important;
    align-items: center !important;
  }

  .innovation-text {
    font-size: 2rem !important;
    margin-bottom: 10px !important;
    line-height: 1.1;
  }

  .logo-bg {
    width: 50px;
    height: 50px;
  }

  .logo-container {
    margin-bottom: -20px !important;
  }

  .centered-content {
    margin-top: 40px !important;
  }

  .action-buttons {
    margin-top: 15px !important;
  }

  .q-gutter-md {
    margin: 8px !important;
  }

  .q-gutter-md > * {
    margin: 8px !important;
  }

  .explore-btn,
  .signin-btn {
    margin: 8px 16px !important;
    width: calc(100% - 32px) !important;
  }
}

.animate-slide-down {
  opacity: 0;
  transform: translateY(-30px);
  animation: slideDown 0.6s ease forwards;
}

.animate-slide-right {
  opacity: 0;
  transform: translateX(-30px);
  animation: slideRight 0.6s ease 0.3s forwards;
}

.animate-fade-in {
  opacity: 0;
  animation: fadeIn 0.6s ease 0.6s forwards;
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.6s ease 0.3s forwards;
}

.animate-bounce {
  animation: bounce 2s infinite;
  opacity: 0;
  animation-delay: 0.9s;
  animation-fill-mode: forwards;
}

.animate-wave {
  animation: wave 3s ease-in-out infinite;
}

@keyframes slideDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
    opacity: 1;
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>
