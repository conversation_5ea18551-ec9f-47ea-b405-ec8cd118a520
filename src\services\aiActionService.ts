/**
 * AI Action Service
 *
 * Handles execution of AI action buttons including navigation, dialogs, and triggers
 */

import { Dialog } from 'quasar'
import type { AIActionButton } from './aiChatService'
import { triggerSignIn, triggerSignUp } from './unifiedAuthService'

interface ActionExecutionResult {
  success: boolean
  message?: string
  error?: string
}

class AIActionService {
  /**
   * Execute an AI action button
   */
  async executeAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      console.log('🎯 Executing AI action:', action)

      // Check authentication requirement
      if (action.requires_auth) {
        const { useAuthStore } = await import('@/stores/auth')
        const authStore = useAuthStore()
        if (!authStore.isAuthenticated) {
          console.log('🔒 Action requires authentication, triggering auth dialog')
          return await this.executeDialogAction({
            ...action,
            action_type: 'dialog',
            action_data: { dialog: 'auth-signin' }
          })
        }
      }

      // Execute action based on type
      switch (action.action_type) {
        case 'navigation':
          return await this.executeNavigationAction(action)
        
        case 'dialog':
          return await this.executeDialogAction(action)
        
        case 'trigger':
          return await this.executeTriggerAction(action)
        
        case 'external':
          return await this.executeExternalAction(action)
        
        default:
          throw new Error(`Unknown action type: ${action.action_type}`)
      }

    } catch (error: any) {
      console.error('❌ Error executing AI action:', error)
      return {
        success: false,
        error: error.message || 'Failed to execute action'
      }
    }
  }

  /**
   * Execute navigation action
   */
  private async executeNavigationAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { route, params } = action.action_data

      if (!route) {
        throw new Error('Navigation action missing route')
      }

      console.log('🧭 Navigating to:', { route, params })

      // Use window.location for navigation to avoid router composable issues
      let targetUrl = route

      // Add query parameters if provided
      if (params && Object.keys(params).length > 0) {
        const searchParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            searchParams.append(key, String(value))
          }
        })
        const queryString = searchParams.toString()
        if (queryString) {
          targetUrl += (targetUrl.includes('?') ? '&' : '?') + queryString
        }
      }

      // Navigate using window.location
      window.location.href = targetUrl

      return {
        success: true,
        message: `Navigated to ${route}`
      }

    } catch (error: any) {
      console.error('❌ Navigation error:', error)
      return {
        success: false,
        error: `Navigation failed: ${error.message}`
      }
    }
  }

  /**
   * Execute dialog action
   */
  private async executeDialogAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { dialog, params } = action.action_data

      if (!dialog) {
        throw new Error('Dialog action missing dialog type')
      }

      console.log('💬 Opening dialog:', { dialog, params })

      switch (dialog) {
        case 'auth-signin':
        case 'signin':
          triggerSignIn()
          return {
            success: true,
            message: 'Sign in dialog opened'
          }

        case 'auth-signup':
        case 'signup':
          triggerSignUp()
          return {
            success: true,
            message: 'Sign up dialog opened'
          }

        case 'profile-completion':
          await this.openProfileCompletionDialog(params)
          return {
            success: true,
            message: 'Opened profile completion dialog'
          }

        case 'message-user':
          await this.openMessageDialog(params)
          return {
            success: true,
            message: 'Opened message dialog'
          }

        case 'post-creation':
          await this.openPostCreationDialog(params)
          return {
            success: true,
            message: 'Opened post creation dialog'
          }

        default:
          throw new Error(`Unknown dialog type: ${dialog}`)
      }

    } catch (error: any) {
      console.error('❌ Dialog error:', error)
      return {
        success: false,
        error: `Dialog failed: ${error.message}`
      }
    }
  }

  /**
   * Execute trigger action
   */
  private async executeTriggerAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { trigger_key, message, params } = action.action_data

      console.log('🎯 Executing trigger:', { trigger_key, message, params })

      // Import trigger service dynamically to avoid circular dependencies
      const { useAiChatTriggerService } = await import('./aiChatTriggerService')
      const triggerService = useAiChatTriggerService()

      if (message) {
        // Direct message trigger
        await triggerService.sendMessage(message)
        return {
          success: true,
          message: `Sent message: ${message}`
        }
      } else if (trigger_key) {
        // Predefined trigger key
        await triggerService.triggerChat(trigger_key, params?.context)
        return {
          success: true,
          message: `Triggered ${trigger_key}`
        }
      } else {
        throw new Error('Trigger action missing both trigger_key and message')
      }

    } catch (error: any) {
      console.error('❌ Trigger error:', error)
      return {
        success: false,
        error: `Trigger failed: ${error.message}`
      }
    }
  }

  /**
   * Execute external action
   */
  private async executeExternalAction(action: AIActionButton): Promise<ActionExecutionResult> {
    try {
      const { url } = action.action_data

      if (!url) {
        throw new Error('External action missing URL')
      }

      console.log('🌐 Opening external URL:', url)

      // Open in new tab/window
      window.open(url, '_blank', 'noopener,noreferrer')

      return {
        success: true,
        message: `Opened ${url}`
      }

    } catch (error: any) {
      console.error('❌ External action error:', error)
      return {
        success: false,
        error: `External action failed: ${error.message}`
      }
    }
  }

  /**
   * Open profile completion dialog
   */
  private async openProfileCompletionDialog(params?: Record<string, any>): Promise<void> {
    // Navigate to dashboard with profile completion focus using window.location
    const queryParams = new URLSearchParams({ action: 'complete-profile', ...params })
    window.location.href = `/dashboard?${queryParams.toString()}`
  }

  /**
   * Open message dialog
   */
  private async openMessageDialog(params?: Record<string, any>): Promise<void> {
    if (!params?.userId) {
      throw new Error('Message dialog requires userId parameter')
    }

    const MessageDialog = (await import('@/components/messaging/MessageDialog.vue')).default

    Dialog.create({
      component: MessageDialog,
      componentProps: {
        userId: params.userId,
        userName: params.userName || 'User'
      }
    })
  }

  /**
   * Open post creation dialog
   */
  private async openPostCreationDialog(params?: Record<string, any>): Promise<void> {
    // Navigate to community with post creation using window.location
    const queryParams = new URLSearchParams({
      tab: 'feed',
      action: 'create-post',
      type: params?.postType || 'general',
      ...params
    })
    window.location.href = `/virtual-community?${queryParams.toString()}`
  }

  /**
   * Generate context-aware action buttons based on user state
   */
  generateContextualActions(userContext: any, currentPage: string): AIActionButton[] {
    const actions: AIActionButton[] = []

    // Use userContext instead of direct store access to avoid composable issues

    // Authentication-based actions
    if (!userContext.is_authenticated) {
      actions.push({
        id: 'auth-signin',
        label: 'Sign In',
        icon: 'login',
        color: 'primary',
        action_type: 'dialog',
        action_data: { dialog: 'auth-signin' },
        tooltip: 'Sign in to access all features'
      })

      actions.push({
        id: 'auth-signup',
        label: 'Join Platform',
        icon: 'person_add',
        color: 'secondary',
        action_type: 'dialog',
        action_data: { dialog: 'auth-signup' },
        tooltip: 'Create your account'
      })
    } else {
      // Authenticated user actions
      const profileCompletion = userContext.profile_completion || 0

      // Profile completion actions
      if (profileCompletion < 80) {
        actions.push({
          id: 'complete-profile',
          label: 'Complete Profile',
          icon: 'person',
          color: 'orange',
          action_type: 'dialog',
          action_data: { dialog: 'profile-completion' },
          tooltip: 'Complete your profile for better visibility',
          requires_auth: true
        })
      }

      // Page-specific actions
      if (currentPage === 'home') {
        actions.push({
          id: 'go-dashboard',
          label: 'Go to Dashboard',
          icon: 'dashboard',
          color: 'primary',
          action_type: 'navigation',
          action_data: { route: '/dashboard' },
          tooltip: 'Access your personalized dashboard',
          requires_auth: true
        })
      }

      if (currentPage !== 'virtual-community') {
        actions.push({
          id: 'explore-community',
          label: 'Explore Community',
          icon: 'people',
          color: 'secondary',
          action_type: 'navigation',
          action_data: { route: '/virtual-community', params: { tab: 'feed' } },
          tooltip: 'Discover the innovation community'
        })
      }
    }

    return actions
  }
}

// Create singleton instance
let _aiActionService: AIActionService | null = null

export const useAiActionService = (): AIActionService => {
  if (!_aiActionService) {
    _aiActionService = new AIActionService()
  }
  return _aiActionService
}

// Default export for compatibility
export default useAiActionService()
