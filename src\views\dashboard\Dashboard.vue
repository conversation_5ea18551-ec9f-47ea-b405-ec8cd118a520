<template>
  <q-page class="q-pa-md">
    <!-- Loading overlay -->
    <div v-if="isLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading dashboard...</div>
    </div>

    <!-- Main content - only show when not in loading state -->
    <div v-show="!isLoading">
      <div class="row q-col-gutter-md">
        <!-- Welcome Section with Countdown -->
        <div class="col-12">
          <q-card class="welcome-card bg-light-green-1">
            <q-card-section>
              <div class="row">
                <div class="col-12 col-md-7">
                  <div class="text-h5 text-green-9">Welcome to ZbInnovation</div>
                  <div class="text-subtitle1 q-mt-sm">
                    Welcome back, {{ authStore.currentUser?.email || '<EMAIL>' }}!
                  </div>
                  <div class="text-body1 q-mt-md">
                    <template v-if="!hasAnyProfileData">
                      Create your first profile to get started with ZbInnovation and
                      connect with like-minded innovators!
                    </template>
                    <template v-else-if="!isProfileComplete">
                      Complete your profile to get the most out of ZbInnovation and
                      connect with like-minded innovators!
                    </template>
                    <template v-else>
                      Welcome to your dashboard! Here you can manage your profiles and stay updated
                      with the latest events and opportunities.
                    </template>
                  </div>

                  <!-- Message notification alert if there are unread messages -->
                  <div v-if="unreadMessageCount > 0" class="q-mt-md">
                    <q-banner class="bg-red-1 text-red-9" rounded>
                      <template v-slot:avatar>
                        <q-icon name="chat" color="red-9" />
                      </template>
                      <div class="text-weight-medium">
                        You have {{ unreadMessageCount }} unread message{{ unreadMessageCount > 1 ? 's' : '' }}
                      </div>
                      <template v-slot:action>
                        <q-btn flat color="red-9" label="View Messages" to="/dashboard/messages" />
                      </template>
                    </q-banner>
                  </div>

                  <!-- Connection requests notification alert -->
                  <div v-if="connectionRequestsCount > 0" class="q-mt-md">
                    <q-banner class="bg-purple-1 text-purple-9" rounded>
                      <template v-slot:avatar>
                        <q-icon name="person_add" color="purple-9" />
                      </template>
                      <div class="text-weight-medium">
                        You have {{ connectionRequestsCount }} connection request{{ connectionRequestsCount > 1 ? 's' : '' }}
                      </div>
                      <template v-slot:action>
                        <q-btn flat color="purple-9" label="View Requests" to="/dashboard/connections" />
                      </template>
                    </q-banner>
                  </div>
                </div>
                <div class="col-12 col-md-5 feature-column">
                  <div class="feature-wrapper">
                    <div class="feature-container" :class="{ 'feature-mobile': $q.screen.lt.sm }">
                      <div class="text-subtitle1 feature-label q-mb-sm">Now Live!</div>
                      <div class="feature-content">
                        <q-icon name="celebration" size="2rem" color="primary" class="q-mr-sm" />
                        <div class="text-body1">
                          Explore our virtual community and connect with innovators across Zimbabwe!
                        </div>
                      </div>
                      <div class="community-buttons-container q-mt-md">
                        <div class="q-mb-sm">
                          <q-btn
                            color="primary"
                            label="Explore Community"
                            class="full-width dashboard-action-btn"
                            outline
                            @click="navigateToVirtualCommunity"
                            :loading="communityButtonLoading"
                            :disable="communityButtonLoading"
                          />
                        </div>
                        <div>
                          <q-btn
                            color="purple"
                            label="AI Guide"
                            icon="smart_toy"
                            class="full-width dashboard-action-btn"
                            outline
                            @click="handleAITrigger('community_exploration')"
                            :loading="aiTriggerLoading"
                            :disable="aiTriggerLoading"
                          >
                            <q-tooltip>Get AI assistance for exploring the community</q-tooltip>
                          </q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Profile Type and Status Cards - Only shown for users with profiles -->
        <template v-if="hasAnyProfileData">
          <div class="col-12 col-md-6">
            <q-card class="profile-type-card">
              <q-card-section>
                <div class="row justify-between items-center">
                  <div class="text-h6">Profile Type</div>
                  <q-icon name="lightbulb" size="24px" class="text-grey-8" />
                </div>
                <div class="q-mt-md text-center">
                  <q-badge :color="profileStore.currentProfile?.profile_type ? getProfileTypeColor(profileStore.currentProfile.profile_type) : 'purple'" class="profile-type-badge">
                    <div class="text-subtitle1 q-py-xs q-px-md">{{ profileStore.currentProfile?.profile_type ? formatProfileType(profileStore.currentProfile.profile_type) : 'Innovator' }}</div>
                  </q-badge>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-6">
            <q-card class="profile-status-card">
              <q-card-section>
                <div class="row justify-between items-center">
                  <div class="text-h6">Profile Status</div>
                  <q-icon name="verified" size="24px" class="text-grey-8" />
                </div>
                <div class="q-mt-md text-center">
                  <q-badge :color="profileStore.currentProfile?.profile_state ? getProfileStateColor(profileStore.currentProfile.profile_state) : 'green'" class="profile-status-badge">
                    <div class="text-subtitle1 q-py-xs q-px-md">{{ profileStore.currentProfile?.profile_state || 'IN_PROGRESS' }}</div>
                  </q-badge>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </template>

        <!-- Profile Completion Status -->
        <div class="col-12" v-if="profileStore.currentProfile && hasAnyProfileData">
          <profile-completion-status
            :profile="profileStore.currentProfile"
            :compact="true"
          />
        </div>

        <!-- Create Your First Profile - Only shown for users without profiles -->
        <div v-if="!hasAnyProfileData" class="col-12">
          <q-card class="q-pa-lg text-center">
            <q-card-section>
              <unified-icon name="person_add" size="64px" class="text-green-9 q-mb-md" />
              <div class="text-h5 text-green-9">Create Your First Profile</div>
              <p class="text-body1 q-my-md">To get started, create your first profile by selecting your role in the ecosystem.</p>
              <q-btn
                color="green-9"
                label="Create Your Profile"
                :to="{ name: 'profile-create' }"
                size="lg"
                class="q-mt-md"
              >
                <template v-slot:prepend>
                  <unified-icon name="person_add" class="q-mr-xs" />
                </template>
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- Type-specific Dashboard - Only shown for users with profiles -->
        <div v-if="hasAnyProfileData" class="col-12">
          <type-specific-dashboard :profile-type="profileStore.currentProfile?.profile_type" />
        </div>

        <!-- User Content Management removed as requested -->

        <!-- AI Features Card -->
        <div class="col-12">
          <AIFeaturesCard />
        </div>

        <!-- Profile-Aware AI Triggers -->
        <div v-if="hasAnyProfileData" class="col-12">
          <ProfileAwareAITriggers
            context="dashboard"
            :show-matchmaking="true"
            :show-profile-enhancement="!isProfileComplete"
            :show-quick-actions="true"
            :profile-type="profileStore.currentProfile?.profile_type"
          />
        </div>

        <!-- Dashboard Summary Cards -->
        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1 text-primary">
                    Recent Activity
                    <notification-badge
                      v-if="unreadActivitiesCount > 0"
                      :count="unreadActivitiesCount"
                      color="blue"
                      rounded
                      class="q-ml-sm"
                    />
                  </div>
                  <div class="text-caption text-grey">Latest updates and interactions</div>
                </div>
                <div>
                  <q-btn
                    flat
                    color="primary"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/activity"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <activity-feed :limit="3" title="" />
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1 text-primary">
                    Connections
                    <notification-badge
                      v-if="connectionRequestsCount > 0"
                      :count="connectionRequestsCount"
                      color="red"
                      rounded
                      class="q-ml-sm"
                    />
                  </div>
                  <div class="text-caption text-grey">Your network and requests</div>
                </div>
                <div>
                  <q-btn
                    flat
                    color="primary"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/connections"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <connection-requests :limit="3" title="" />
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1 text-primary">
                    Bookmarks
                    <q-badge v-if="totalSavedItems > 0" color="cyan" class="q-ml-sm">
                      {{ totalSavedItems }}
                    </q-badge>
                  </div>
                  <div class="text-caption text-grey">Your saved content</div>
                </div>
                <div>
                  <q-btn
                    flat
                    color="primary"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/activity"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <div v-if="loadingBookmarks" class="text-center q-pa-md">
                <q-spinner color="primary" size="2em" />
                <div class="q-mt-sm">Loading bookmarks...</div>
              </div>

              <div v-else-if="recentBookmarks.length === 0" class="text-center q-pa-md">
                <unified-icon name="bookmark_border" size="2em" color="grey-5" />
                <div class="text-subtitle2 q-mt-sm">No bookmarks yet</div>
                <div class="text-caption q-mt-sm">
                  Save posts, profiles, and other content to see them here.
                </div>
              </div>

              <q-list v-else>
                <q-item
                  v-for="bookmark in recentBookmarks"
                  :key="bookmark.id"
                  clickable
                  @click="viewBookmarkedContent(bookmark)"
                >
                  <q-item-section avatar>
                    <q-avatar color="cyan" text-color="white">
                      <unified-icon :name="getBookmarkIcon(bookmark)" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ getBookmarkTitle(bookmark) }}</q-item-label>
                    <q-item-label caption>{{ getBookmarkType(bookmark) }} • {{ formatDate(bookmark.created_at) }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1 text-primary">Upcoming Events</div>
                  <div class="text-caption text-grey">Events and workshops</div>
                </div>
                <div>
                  <q-btn
                    flat
                    color="primary"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/events"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <!-- Simple events preview -->
              <q-list>
                <q-item v-for="i in 2" :key="i" clickable to="/dashboard/events">
                  <q-item-section avatar>
                    <q-avatar color="primary" text-color="white">
                      <unified-icon name="event" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Innovation Workshop {{ i }}</q-item-label>
                    <q-item-label caption>July {{ 15 + i }}, 2025</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1 text-primary">Announcements</div>
                  <div class="text-caption text-grey">Platform updates and news</div>
                </div>
                <div>
                  <q-btn
                    flat
                    color="primary"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/announcements"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <!-- Simple announcements preview -->
              <q-list>
                <q-item v-for="i in 2" :key="i" clickable to="/dashboard/announcements">
                  <q-item-section avatar>
                    <q-avatar color="primary" text-color="white">
                      <unified-icon name="campaign" />
                    </q-avatar>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>Platform Update {{ i }}</q-item-label>
                    <q-item-label caption>August {{ 10 + i }}, 2025</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="dashboard-summary-card" :class="{'message-card-highlight': unreadMessageCount > 0}">
            <q-card-section :class="unreadMessageCount > 0 ? 'bg-red-2' : 'bg-grey-2'">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-subtitle1" :class="unreadMessageCount > 0 ? 'text-red-9' : 'text-primary'">
                    Messages
                    <q-badge v-if="unreadMessageCount > 0" color="red" class="q-ml-sm">
                      {{ unreadMessageCount }}
                    </q-badge>
                  </div>
                  <div class="text-caption" :class="unreadMessageCount > 0 ? 'text-red-8' : 'text-grey'">
                    {{ unreadMessageCount > 0 ? 'You have unread messages' : 'Recent conversations' }}
                  </div>
                </div>
                <div>
                  <q-btn
                    flat
                    :color="unreadMessageCount > 0 ? 'red-9' : 'primary'"
                    label="View All"
                    icon-right="arrow_forward"
                    to="/dashboard/messages"
                    size="sm"
                  />
                </div>
              </div>
            </q-card-section>
            <q-card-section>
              <div v-if="loadingConversations" class="text-center q-pa-md">
                <q-spinner color="primary" size="2em" />
                <div class="q-mt-sm">Loading conversations...</div>
              </div>

              <div v-else-if="conversations.length === 0" class="text-center q-pa-md">
                <unified-icon name="chat" size="2em" color="grey-5" />
                <div class="text-subtitle2 q-mt-sm">No messages yet</div>
                <div class="text-caption q-mt-sm">
                  Start a conversation by messaging someone from their profile.
                </div>
              </div>

              <q-list v-else>
                <q-item
                  v-for="conversation in conversations.slice(0, 3)"
                  :key="conversation.id"
                  clickable
                  to="/dashboard/messages"
                  :class="{'bg-red-1': !conversation.is_read && conversation.recipient_id === currentUserId}"
                >
                  <q-item-section avatar>
                    <user-avatar
                      :user-id="conversation.other_user_id"
                      :name="getUserName(conversation.other_user)"
                      :email="conversation.other_user?.email || ''"
                      :avatar-url="conversation.other_user?.avatar_url"
                      size="40px"
                    />
                  </q-item-section>

                  <q-item-section>
                    <q-item-label>
                      {{ getUserName(conversation.other_user) }}
                      <q-badge v-if="!conversation.is_read && conversation.recipient_id === currentUserId"
                        color="red" class="q-ml-sm" rounded>New</q-badge>
                    </q-item-label>
                    <q-item-label caption lines="1" class="ellipsis" :class="{'text-weight-bold': !conversation.is_read && conversation.recipient_id === currentUserId}">
                      {{ conversation.content }}
                    </q-item-label>
                    <q-item-label caption>
                      {{ formatDate(conversation.created_at) }}
                    </q-item-label>
                  </q-item-section>

                  <q-item-section side v-if="!conversation.is_read && conversation.recipient_id === currentUserId">
                    <q-badge color="red" floating />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <!-- Upcoming Events - LIVE FEATURE -->
        <div id="events" class="col-12">
          <q-card class="events-card">
            <q-card-section>
              <div class="text-h6">Upcoming Events</div>
              <div class="q-mt-md">
                <q-list bordered separator class="rounded-borders">
                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="event" class="text-green-9" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>ZbInnovation Launch</q-item-label>
                      <q-item-label caption>April 25th, 2025</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="green-9">Coming Soon</q-badge>
                    </q-item-section>
                  </q-item>

                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="groups" class="text-grey-8" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Early Access Networking Event</q-item-label>
                      <q-item-label caption>Virtual - Date to be announced</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="grey-8">Coming Soon</q-badge>
                    </q-item-section>
                  </q-item>

                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="school" class="text-green-9" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Innovation Workshop Series</q-item-label>
                      <q-item-label caption>Online - Starting Summer 2025</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="orange">Planned</q-badge>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- COMING SOON FEATURES - Moved to bottom -->
        <div class="col-12">
          <q-separator class="q-my-md" />
          <div class="text-h6 q-mb-md">Coming Soon Features</div>
        </div>

        <!-- Feature Roadmap - COMING SOON -->
        <div class="col-12 col-md-6">
          <feature-roadmap />
        </div>

        <!-- Marketplace Preview - COMING SOON -->
        <div class="col-12 col-md-6">
          <matchmaking-preview />
        </div>

        <!-- My Profiles Section -->
        <div class="col-12">
          <q-card>
            <q-card-section>
              <div class="row items-center justify-between q-mb-md">
                <div class="text-h6">My Profiles</div>
                <q-btn
                  v-if="profileStore.userProfiles.filter(p => p.profile_type).length > 0"
                  color="green-9"
                  outline
                  label="Create New Profile"
                  icon="add"
                  :to="{ name: 'profile-create' }"
                />
              </div>
              <div v-if="profileStore.userProfiles.filter(p => p.profile_type).length > 0">
                <q-list bordered separator class="rounded-borders">
                  <q-item
                    v-for="profile in profileStore.userProfiles.filter(p => p.profile_type)"
                    :key="profile.user_id"
                    clickable
                    @click="selectProfile(profile.user_id)"
                    :active="profileStore.currentProfile?.user_id === profile.user_id"
                  >
                    <q-item-section avatar>
                      <q-avatar :color="getProfileTypeColor(profile.profile_type)" text-color="white">
                        {{ (profile.first_name?.[0] || '') + (profile.last_name?.[0] || '') || '?' }}
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ profile.profile_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Unnamed Profile' }}</q-item-label>
                      <q-item-label caption class="row items-center">
                        <q-badge :color="getProfileTypeColor(profile.profile_type)" text-color="white" class="q-mr-xs">
                          {{ formatProfileType(profile.profile_type) }}
                        </q-badge>
                        <q-badge :color="getProfileStateColor(profile.profile_state)" text-color="white" class="q-mr-xs">
                          {{ profile.profile_state }}
                        </q-badge>
                        <q-linear-progress
                          :value="(profile.profile_completion || 0) / 100"
                          :color="profile.profile_completion >= 100 ? 'positive' : 'primary'"
                          class="q-ml-sm"
                          style="width: 60px; height: 6px;"
                        />
                        <span class="q-ml-xs text-caption" v-if="false">{{ Math.round(profile.profile_completion || 0) }}%</span>
                      </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <div class="row q-gutter-sm">
                        <q-btn flat round size="sm" color="green-9" icon="visibility" @click="viewProfile(profile.user_id)">
                          <q-tooltip>View Profile</q-tooltip>
                        </q-btn>
                        <q-btn flat round size="sm" color="green-9" icon="message" @click="messageProfile(profile.user_id)">
                          <q-tooltip>Message</q-tooltip>
                        </q-btn>
                        <q-btn flat round size="sm" color="green-9" icon="person_add" @click="followProfile(profile.user_id)">
                          <q-tooltip>Follow</q-tooltip>
                        </q-btn>
                        <q-btn flat round size="sm" color="green-9" icon="edit" :to="{ name: 'profile-edit', params: { id: profile.user_id } }">
                          <q-tooltip>Edit Profile</q-tooltip>
                        </q-btn>
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
              <div v-else class="text-center q-pa-md">
                <q-icon name="person_add" size="3rem" color="grey-5" />
                <p class="text-subtitle1 q-mt-sm">You need to create a profile with a category to get started.</p>
                <q-btn label="Create Your Profile" icon="add" :to="{ name: 'profile-create' }" class="zb-btn-primary q-mt-md" />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Profile completion popup -->
    <q-dialog v-model="showProfileCompletionPopup" persistent>
      <profile-completion-popup
        v-model="showProfileCompletionPopup"
        :is-initial="isNewUser"
        @remind-later="handleRemindLater"
      />
    </q-dialog>

    <!-- Profile creation modal -->
    <profile-creation-modal
      v-model="showProfileCreationModal"
      @profile-created="handleProfileCreated"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { date } from 'quasar'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import { useActivityNotificationsStore } from '../../stores/activityNotifications'
import { supabase } from '../../lib/supabase'
import { serviceCoordinator } from '../../services/ServiceCoordinator'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'
import NotificationBadge from '../../components/common/NotificationBadge.vue'
import ProfileCompletionPopup from '../../components/profile/ProfileCompletionPopup.vue'
import ProfileCreationModal from '../../components/profile/ProfileCreationModal.vue'
import ProfileCompletionStatus from '../../components/profile/ProfileCompletionStatus.vue'
import TypeSpecificDashboard from '../../components/dashboard/TypeSpecificDashboard.vue'
import FeatureRoadmap from '../../components/dashboard/FeatureRoadmap.vue'
import MatchmakingPreview from '../../components/dashboard/MatchmakingPreview.vue'
import ActivityFeed from '../../components/activity/ActivityFeed.vue'
import ConnectionsList from '../../components/connections/ConnectionsList.vue'
import ConnectionRequests from '../../components/connections/ConnectionRequests.vue'
import UserAvatar from '../../components/common/UserAvatar.vue'
import AIFeaturesCard from '../../components/ai/AIFeaturesCard.vue'
import ProfileAwareAITriggers from '../../components/ai/ProfileAwareAITriggers.vue'
import { formatProfileType, getProfileTypeIcon } from '../../services/profileTypes'
import { useMessagingStore, type Conversation } from '../../stores/messaging'
import { getUniversalUsername } from '../../utils/userUtils'
import { useUserInteractionsStore } from '../../stores/userInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'

// Import simplified services
import { useUserState } from '../../services/userStateService'
import { useProfileCompletion } from '../../services/profileCompletionService'

const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()
const globalServices = useGlobalServicesStore()
const router = useRouter()

// Use simplified services
const { isNewUser, hasIncompleteProfile, hasAnyProfileData, isLoading: userStateLoading, checkUserState, profileData } = useUserState()
const { isProfileComplete } = useProfileCompletion()

// Simple loading state
const isLoading = ref(true)

// UI state
const showProfileCompletionPopup = ref(false)
const showProfileCreationModal = ref(false)
const communityButtonLoading = ref(false)
const aiTriggerLoading = ref(false)

// Messaging and activity state
const messagingStore = useMessagingStore()
const activityNotificationsStore = useActivityNotificationsStore()
const userInteractionsStore = useUserInteractionsStore()
const conversations = ref<Conversation[]>([])
const loadingConversations = ref(false)
const loadingBookmarks = ref(false)
const currentUserId = computed(() => authStore.currentUser?.id || '')

// Notification counts
const unreadMessageCount = computed(() => messagingStore.unreadCount)
const connectionRequestsCount = computed(() => activityNotificationsStore.connectionRequests)
const unreadActivitiesCount = computed(() => activityNotificationsStore.unreadActivities)
const totalActivityCount = computed(() => activityNotificationsStore.totalUnreadCount)

// Bookmarks data
const totalSavedItems = computed(() => userInteractionsStore.totalSavedItems)
const recentBookmarks = computed(() => {
  // Combine saved posts and profiles, sort by date, take first 3
  const allBookmarks = [
    ...userInteractionsStore.savedPosts.map(item => ({
      ...item,
      type: 'post',
      title: item.post?.title || 'Untitled Post',
      content_type: item.post?.post_type || 'post'
    })),
    ...userInteractionsStore.savedProfiles.map(item => ({
      ...item,
      type: 'profile',
      title: item.profile?.name || 'Profile',
      content_type: 'profile'
    }))
  ]

  return allBookmarks
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 3)
})

// Lifecycle hooks
onMounted(async () => {
  isLoading.value = true
  try {
    // Direct check of user state - single database query
    await checkUserState()

    // Load minimal profile data if needed
    if (hasAnyProfileData.value && !profileStore.currentProfile) {
      await profileStore.loadUserProfiles()
    }

    // Services are coordinated by DashboardLayout via ServiceCoordinator
    // This prevents duplicate API calls and improves performance
    console.log('Dashboard: Using ServiceCoordinator for service management')
    console.log('Service initialization status:', serviceCoordinator.getStats())

    // Wait for critical services to be ready
    const servicesReady = await serviceCoordinator.waitForAllServices(10000) // 10 second timeout
    if (servicesReady) {
      console.log('✅ Dashboard: All services ready')
      console.log('Unread message count:', messagingStore.unreadCount)
      console.log('Connection requests:', connectionRequestsCount.value)
      console.log('Unread activities:', unreadActivitiesCount.value)
    } else {
      console.warn('⚠️ Dashboard: Some services may not be ready yet')
    }

    // Add a click event listener to the document to mark notifications as viewed when clicked
    document.addEventListener('click', (event) => {
      // Check if the clicked element is a notification or contains a notification
      const target = event.target as HTMLElement;
      const notificationElement = target.closest('.notification-badge, .q-badge');

      if (notificationElement) {
        console.log('Notification clicked, marking as viewed');
        // Mark both types of notifications as viewed
        activityNotificationsStore.markConnectionRequestsAsViewed();
        activityNotificationsStore.markActivitiesAsRead();
      }
    })

    // Load conversations for the messages card
    await loadConversations()

    // Load bookmarks data
    await loadBookmarks()
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    isLoading.value = false
  }
})

// Computed
const welcomeMessage = computed(() => {
  if (authStore.currentUser?.email) {
    return `Welcome back, ${authStore.currentUser.email}!`
  }
  return 'Welcome to your dashboard!'
})

// Methods
function getProfileStateColor(state: string | undefined): string {
  if (!state) return 'grey'

  const colors: Record<string, string> = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }

  return colors[state] || 'grey'
}

function getProfileTypeColor(type: string | null | undefined): string {
  if (!type) return 'grey'

  const colors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }

  return colors[type] || 'grey'
}

function selectProfile(profileId: string): void {
  profileStore.setCurrentProfile(profileId)
}

// View a profile
function viewProfile(profileId: string): void {
  console.log('Viewing profile:', profileId)
  // Navigate to the public profile view
  router.push({ name: 'user-profile', params: { id: profileId } })
}

// Handle messaging a profile
async function messageProfile(profileId: string): Promise<void> {
  console.log('Messaging profile:', profileId)

  // Check if user is authenticated
  const { data } = await supabase.auth.getUser();
  const user = data.user;

  if (!user) {
    notifications.warning('Please sign in to send messages');
    router.push({ name: 'sign-in', query: { redirect: router.currentRoute.value.fullPath } });
    return;
  }

  // Navigate to the profile view with a query parameter to open the message dialog
  router.push({
    name: 'user-profile',
    params: { id: profileId },
    query: { action: 'message' }
  });
}

// Handle following a profile
function followProfile(profileId: string): void {
  console.log('Following profile:', profileId)
  // TODO: Implement follow functionality
  // This would typically update a database record to establish a connection
}

// Handle profile creation events
function handleProfileCreated(profile) {
  console.log('Profile created:', profile)
  // Force reload profiles to update the UI
  profileStore.loadUserProfiles()
}

// Handle loading state changes from profile creation
function handleProfileCreationLoadingChange(isLoading) {
  // This could be used to show a loading indicator if needed
  console.log('Profile creation loading state changed:', isLoading)
}

// Handle remind later for profile completion popup
function handleRemindLater() {
  // Store the current time in localStorage
  localStorage.setItem('profileCompletionRemindLater', Date.now().toString())
  showProfileCompletionPopup.value = false
}

// Navigate to virtual community feed
async function navigateToVirtualCommunity() {
  communityButtonLoading.value = true
  try {
    await router.push('/virtual-community?tab=feed')
  } catch (error) {
    console.error('Navigation error:', error)
  } finally {
    communityButtonLoading.value = false
  }
}

// Handle AI trigger clicks
async function handleAITrigger(triggerKey: string) {
  aiTriggerLoading.value = true
  try {
    await globalServices.aiChatTriggerService.triggerChat(triggerKey, 'dashboard-community')
  } catch (error) {
    console.error('Error triggering AI chat:', error)
  } finally {
    aiTriggerLoading.value = false
  }
}

// Messaging functions
async function loadConversations() {
  loadingConversations.value = true

  try {
    const result = await messagingStore.loadConversations()
    conversations.value = result
  } catch (err: any) {
    console.error('Error loading conversations:', err)
  } finally {
    loadingConversations.value = false
  }
}

function getUserName(user: any): string {
  return getUniversalUsername(user);
}

function formatDate(dateStr: string): string {
  return date.formatDate(dateStr, 'MMM D, YYYY h:mm A')
}

// Bookmark functions
async function loadBookmarks() {
  loadingBookmarks.value = true
  try {
    await userInteractionsStore.fetchAllUserInteractions()
  } catch (err: any) {
    console.error('Error loading bookmarks:', err)
  } finally {
    loadingBookmarks.value = false
  }
}

function getBookmarkIcon(bookmark: any): string {
  switch (bookmark.type) {
    case 'post':
      return 'article'
    case 'profile':
      return 'person'
    default:
      return 'bookmark'
  }
}

function getBookmarkTitle(bookmark: any): string {
  return bookmark.title || 'Untitled'
}

function getBookmarkType(bookmark: any): string {
  switch (bookmark.type) {
    case 'post':
      return bookmark.content_type === 'marketplace' ? 'Marketplace' : 'Post'
    case 'profile':
      return 'Profile'
    default:
      return 'Content'
  }
}

function viewBookmarkedContent(bookmark: any) {
  if (bookmark.type === 'post') {
    router.push(`/virtual-community/post/${bookmark.post_id}`)
  } else if (bookmark.type === 'profile') {
    router.push(`/virtual-community/user/${bookmark.profile_id}`)
  }
}

// No initialization code needed here - everything is handled in onMounted
</script>

<style scoped>
/* Loading overlay */
.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.welcome-card {
  background-color: #f0f8f1;
  color: #0D8A3E;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Feature column and wrapper */
.feature-column {
  display: flex;
  align-items: center;
}

.feature-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Feature container with label on top */
.feature-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  background: rgba(13, 138, 62, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.feature-label {
  white-space: nowrap;
  font-weight: 500;
  color: #0D8A3E;
}

.feature-content {
  display: flex;
  align-items: center;
}

/* Mobile-specific feature styles */
.feature-mobile {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Ensure the feature fits on small screens */
@media (max-width: 599px) {
  .feature-wrapper {
    margin-top: 1rem;
  }
}

/* Profile badges */
.profile-type-badge, .profile-status-badge {
  border-radius: 20px;
  padding: 4px 8px;
}

.profile-type-card, .profile-status-card, .profile-completion-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.profile-completion-card {
  border-left: 4px solid #0D8A3E; /* Green for completion */
}

.profile-type-card {
  border-left: 4px solid var(--q-primary); /* Dynamic color based on profile type */
}

.profile-status-card {
  border-left: 4px solid var(--q-secondary); /* Dynamic color based on profile status */
}

.events-card {
  margin-top: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Progress bar */
.q-linear-progress {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
}

/* Card styling */
.q-card {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.q-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Dashboard summary cards */
.dashboard-summary-card {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  transition: all 0.2s ease;
}

.dashboard-summary-card .q-card__section {
  padding: 16px;
}

.dashboard-summary-card .q-card__section.bg-grey-2 {
  padding: 12px 16px;
}

.dashboard-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Message card highlight for unread messages */
.message-card-highlight {
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  border-left: 4px solid #f44336;
  animation: pulse-red 2s infinite;
}

/* Community action buttons container */
.community-buttons-container {
  width: 100%;
}

/* Dashboard action buttons styling - consistent across all dashboard buttons */
.dashboard-action-btn {
  width: 100%;
  min-height: 44px;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dashboard-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(244, 67, 54, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
  }
}
</style>
